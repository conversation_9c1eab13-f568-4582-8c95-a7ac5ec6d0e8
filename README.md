# <PERSON><PERSON><PERSON><PERSON> Study Buddy Agent

A personalized AI study assistant built with LangChain and Grok API that helps students create study plans, generate quiz questions, and track their learning progress.

## Features

- **Study Plan Creator**: Creates personalized study schedules based on your subjects and available time
- **Quiz Generator**: Generates practice questions on any topic with different difficulty levels
- **Study Tips Provider**: Offers study techniques, memory tips, and learning strategies
- **Progress Tracker**: Remembers your study history and suggests next steps

## Setup

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Create a `.env` file from `.env.example` and add your Grok API key:
```bash
cp .env.example .env
# Edit .env and add your Grok API key from https://console.x.ai/
```

3. Run the application:

**Streamlit Web Interface:**
```bash
streamlit run app.py
```

**Command Line Demo:**
```bash
python cli_demo.py
```

## Usage

1. Start by telling the Study Buddy what subject you want to study
2. Ask for a study plan, quiz questions, or study tips
3. The agent will remember your preferences and progress across sessions

## Example Interactions

- "Create a study plan for calculus exam in 2 weeks"
- "Generate 5 quiz questions about photosynthesis"
- "Give me study tips for memorizing historical dates"
- "What should I study next based on my progress?"
