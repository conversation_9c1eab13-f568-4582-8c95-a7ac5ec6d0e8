"""
Enhanced Progress Tracking System for Study Buddy Agent
Stores and manages user study progress, preferences, and learning history
"""

import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

class StudyProgressTracker:
    """Manages user study progress and learning analytics"""
    
    def __init__(self, user_id: str = "default_user"):
        self.user_id = user_id
        self.data_file = f"study_data_{user_id}.json"
        self.data = self._load_data()
    
    def _load_data(self) -> Dict[str, Any]:
        """Load user data from file or create new data structure"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r') as f:
                    return json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                pass
        
        # Default data structure
        return {
            "user_id": self.user_id,
            "created_date": datetime.now().isoformat(),
            "subjects": {},  # subject_name: {topics: [], difficulty: str, hours_studied: float}
            "study_sessions": [],  # List of study session records
            "quiz_results": [],  # List of quiz attempts and scores
            "study_goals": [],  # List of current and completed goals
            "preferences": {
                "preferred_study_time": None,
                "learning_style": None,
                "difficulty_preference": "medium",
                "session_length": 60  # minutes
            },
            "achievements": [],  # List of unlocked achievements
            "streaks": {
                "current_streak": 0,
                "longest_streak": 0,
                "last_study_date": None
            }
        }
    
    def save_data(self):
        """Save current data to file"""
        try:
            with open(self.data_file, 'w') as f:
                json.dump(self.data, f, indent=2, default=str)
        except Exception as e:
            print(f"Error saving data: {e}")
    
    def add_subject(self, subject_name: str, difficulty: str = "medium"):
        """Add a new subject to track"""
        if subject_name not in self.data["subjects"]:
            self.data["subjects"][subject_name] = {
                "topics": [],
                "difficulty": difficulty,
                "hours_studied": 0.0,
                "last_studied": None,
                "mastery_level": 0  # 0-100 scale
            }
            self.save_data()
    
    def add_topic(self, subject_name: str, topic_name: str, status: str = "not_started"):
        """Add a topic to a subject"""
        if subject_name not in self.data["subjects"]:
            self.add_subject(subject_name)
        
        topic_data = {
            "name": topic_name,
            "status": status,  # not_started, in_progress, completed, mastered
            "date_added": datetime.now().isoformat(),
            "completion_date": None,
            "study_time": 0.0,
            "quiz_scores": []
        }
        
        # Check if topic already exists
        existing_topics = [t["name"] for t in self.data["subjects"][subject_name]["topics"]]
        if topic_name not in existing_topics:
            self.data["subjects"][subject_name]["topics"].append(topic_data)
            self.save_data()
    
    def update_topic_status(self, subject_name: str, topic_name: str, status: str):
        """Update the status of a topic"""
        if subject_name in self.data["subjects"]:
            for topic in self.data["subjects"][subject_name]["topics"]:
                if topic["name"] == topic_name:
                    topic["status"] = status
                    if status == "completed":
                        topic["completion_date"] = datetime.now().isoformat()
                    self.save_data()
                    break
    
    def log_study_session(self, subject_name: str, topic_name: str, duration_minutes: int, notes: str = ""):
        """Log a study session"""
        session = {
            "date": datetime.now().isoformat(),
            "subject": subject_name,
            "topic": topic_name,
            "duration_minutes": duration_minutes,
            "notes": notes
        }
        
        self.data["study_sessions"].append(session)
        
        # Update subject hours
        if subject_name in self.data["subjects"]:
            self.data["subjects"][subject_name]["hours_studied"] += duration_minutes / 60.0
            self.data["subjects"][subject_name]["last_studied"] = datetime.now().isoformat()
        
        # Update topic study time
        if subject_name in self.data["subjects"]:
            for topic in self.data["subjects"][subject_name]["topics"]:
                if topic["name"] == topic_name:
                    topic["study_time"] += duration_minutes / 60.0
                    break
        
        # Update streak
        self._update_streak()
        self.save_data()
    
    def log_quiz_result(self, subject_name: str, topic_name: str, score: float, total_questions: int):
        """Log a quiz result"""
        quiz_result = {
            "date": datetime.now().isoformat(),
            "subject": subject_name,
            "topic": topic_name,
            "score": score,
            "total_questions": total_questions,
            "percentage": (score / total_questions) * 100 if total_questions > 0 else 0
        }
        
        self.data["quiz_results"].append(quiz_result)
        
        # Update topic quiz scores
        if subject_name in self.data["subjects"]:
            for topic in self.data["subjects"][subject_name]["topics"]:
                if topic["name"] == topic_name:
                    topic["quiz_scores"].append(quiz_result["percentage"])
                    break
        
        self.save_data()
    
    def add_goal(self, goal_description: str, target_date: str, subject: str = None):
        """Add a study goal"""
        goal = {
            "id": len(self.data["study_goals"]) + 1,
            "description": goal_description,
            "subject": subject,
            "target_date": target_date,
            "created_date": datetime.now().isoformat(),
            "status": "active",  # active, completed, cancelled
            "completion_date": None
        }
        
        self.data["study_goals"].append(goal)
        self.save_data()
    
    def complete_goal(self, goal_id: int):
        """Mark a goal as completed"""
        for goal in self.data["study_goals"]:
            if goal["id"] == goal_id:
                goal["status"] = "completed"
                goal["completion_date"] = datetime.now().isoformat()
                self.save_data()
                break
    
    def get_progress_summary(self) -> Dict[str, Any]:
        """Get a comprehensive progress summary"""
        total_hours = sum(session["duration_minutes"] for session in self.data["study_sessions"]) / 60.0
        total_subjects = len(self.data["subjects"])
        completed_topics = 0
        total_topics = 0
        
        for subject_data in self.data["subjects"].values():
            for topic in subject_data["topics"]:
                total_topics += 1
                if topic["status"] in ["completed", "mastered"]:
                    completed_topics += 1
        
        recent_sessions = [
            session for session in self.data["study_sessions"]
            if datetime.fromisoformat(session["date"]) > datetime.now() - timedelta(days=7)
        ]
        
        return {
            "total_study_hours": round(total_hours, 1),
            "total_subjects": total_subjects,
            "total_topics": total_topics,
            "completed_topics": completed_topics,
            "completion_rate": round((completed_topics / total_topics) * 100, 1) if total_topics > 0 else 0,
            "current_streak": self.data["streaks"]["current_streak"],
            "longest_streak": self.data["streaks"]["longest_streak"],
            "sessions_this_week": len(recent_sessions),
            "hours_this_week": round(sum(s["duration_minutes"] for s in recent_sessions) / 60.0, 1),
            "active_goals": len([g for g in self.data["study_goals"] if g["status"] == "active"])
        }
    
    def get_subject_progress(self, subject_name: str) -> Optional[Dict[str, Any]]:
        """Get detailed progress for a specific subject"""
        if subject_name not in self.data["subjects"]:
            return None
        
        subject_data = self.data["subjects"][subject_name]
        topics = subject_data["topics"]
        
        completed_topics = [t for t in topics if t["status"] in ["completed", "mastered"]]
        in_progress_topics = [t for t in topics if t["status"] == "in_progress"]
        
        avg_quiz_score = 0
        if self.data["quiz_results"]:
            subject_quizzes = [q for q in self.data["quiz_results"] if q["subject"] == subject_name]
            if subject_quizzes:
                avg_quiz_score = sum(q["percentage"] for q in subject_quizzes) / len(subject_quizzes)
        
        return {
            "subject_name": subject_name,
            "total_topics": len(topics),
            "completed_topics": len(completed_topics),
            "in_progress_topics": len(in_progress_topics),
            "hours_studied": subject_data["hours_studied"],
            "last_studied": subject_data["last_studied"],
            "average_quiz_score": round(avg_quiz_score, 1),
            "mastery_level": subject_data["mastery_level"],
            "next_topics": [t["name"] for t in topics if t["status"] == "not_started"][:3]
        }
    
    def _update_streak(self):
        """Update study streak based on recent activity"""
        today = datetime.now().date()
        last_study_date = self.data["streaks"]["last_study_date"]
        
        if last_study_date:
            last_date = datetime.fromisoformat(last_study_date).date()
            days_diff = (today - last_date).days
            
            if days_diff == 1:  # Consecutive day
                self.data["streaks"]["current_streak"] += 1
            elif days_diff > 1:  # Streak broken
                self.data["streaks"]["current_streak"] = 1
            # If days_diff == 0, same day, don't change streak
        else:
            self.data["streaks"]["current_streak"] = 1
        
        # Update longest streak
        if self.data["streaks"]["current_streak"] > self.data["streaks"]["longest_streak"]:
            self.data["streaks"]["longest_streak"] = self.data["streaks"]["current_streak"]
        
        self.data["streaks"]["last_study_date"] = today.isoformat()
    
    def get_recommendations(self) -> List[str]:
        """Get personalized study recommendations"""
        recommendations = []
        summary = self.get_progress_summary()
        
        # Streak recommendations
        if summary["current_streak"] == 0:
            recommendations.append("Start a new study streak today! Even 15 minutes counts.")
        elif summary["current_streak"] < 3:
            recommendations.append(f"You're on a {summary['current_streak']}-day streak! Keep it going!")
        
        # Subject balance recommendations
        if summary["total_subjects"] > 1:
            subject_hours = {name: data["hours_studied"] for name, data in self.data["subjects"].items()}
            if subject_hours:
                min_subject = min(subject_hours, key=subject_hours.get)
                max_subject = max(subject_hours, key=subject_hours.get)
                if subject_hours[max_subject] > subject_hours[min_subject] * 2:
                    recommendations.append(f"Consider spending more time on {min_subject} to balance your studies.")
        
        # Goal recommendations
        if summary["active_goals"] == 0:
            recommendations.append("Set a study goal to stay motivated and track your progress!")
        
        # Quiz recommendations
        recent_quizzes = [q for q in self.data["quiz_results"] 
                         if datetime.fromisoformat(q["date"]) > datetime.now() - timedelta(days=7)]
        if not recent_quizzes:
            recommendations.append("Take a quiz to test your knowledge and identify areas for improvement.")
        
        return recommendations[:3]  # Return top 3 recommendations
