"""
Test script for the Study Buddy Agent
Run this to verify your setup is working correctly
"""

import os
from dotenv import load_dotenv

def test_environment():
    """Test if environment is set up correctly"""
    print("🔍 Testing Environment Setup...")
    
    # Load environment variables
    load_dotenv()
    
    # Check for API key
    api_key = os.getenv("GROK_API_KEY")
    if not api_key:
        print("❌ GROK_API_KEY not found in environment")
        print("   Please create a .env file with your Grok API key")
        return False
    
    print(f"✅ GROK_API_KEY found (starts with: {api_key[:10]}...)")
    
    # Check base URL
    base_url = os.getenv("GROK_BASE_URL", "https://api.x.ai/v1")
    print(f"✅ GROK_BASE_URL: {base_url}")
    
    return True

def test_imports():
    """Test if all required packages are installed"""
    print("\n📦 Testing Package Imports...")
    
    try:
        import langchain
        print("✅ langchain imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import langchain: {e}")
        return False
    
    try:
        import streamlit
        print("✅ streamlit imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import streamlit: {e}")
        return False
    
    try:
        import requests
        print("✅ requests imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import requests: {e}")
        return False
    
    return True

def test_agent_initialization():
    """Test if the Study Buddy Agent can be initialized"""
    print("\n🤖 Testing Agent Initialization...")
    
    try:
        from study_buddy_agent import StudyBuddyAgent
        print("✅ StudyBuddyAgent class imported successfully")
        
        # Try to initialize (this will test Grok API connection)
        agent = StudyBuddyAgent()
        print("✅ StudyBuddyAgent initialized successfully")
        
        return True, agent
        
    except Exception as e:
        print(f"❌ Failed to initialize StudyBuddyAgent: {e}")
        return False, None

def test_basic_functionality(agent):
    """Test basic agent functionality"""
    print("\n🧪 Testing Basic Functionality...")
    
    try:
        # Test a simple query
        test_message = "Hello, can you help me with studying?"
        print(f"   Testing with message: '{test_message}'")
        
        response = agent.chat(test_message)
        
        if response and len(response) > 10:  # Basic check for meaningful response
            print("✅ Agent responded successfully")
            print(f"   Response preview: {response[:100]}...")
            return True
        else:
            print("❌ Agent response was empty or too short")
            return False
            
    except Exception as e:
        print(f"❌ Error during functionality test: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("🧪 STUDY BUDDY AGENT - TEST SUITE")
    print("=" * 60)
    
    # Test environment
    if not test_environment():
        print("\n❌ Environment test failed. Please fix the issues above.")
        return
    
    # Test imports
    if not test_imports():
        print("\n❌ Import test failed. Please install missing packages:")
        print("   pip install -r requirements.txt")
        return
    
    # Test agent initialization
    success, agent = test_agent_initialization()
    if not success:
        print("\n❌ Agent initialization failed. Check your API key and network connection.")
        return
    
    # Test basic functionality
    if not test_basic_functionality(agent):
        print("\n❌ Functionality test failed. There might be an issue with the Grok API.")
        return
    
    print("\n" + "=" * 60)
    print("🎉 ALL TESTS PASSED!")
    print("Your Study Buddy Agent is ready to use!")
    print("\nNext steps:")
    print("1. Run the Streamlit app: streamlit run app.py")
    print("2. Or try the CLI demo: python cli_demo.py")
    print("=" * 60)

if __name__ == "__main__":
    main()
