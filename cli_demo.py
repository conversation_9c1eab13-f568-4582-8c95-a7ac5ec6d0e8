"""
Command-line demo for the Study Buddy Agent
Simple interface for testing without Streamlit
"""

from study_buddy_agent import StudyBuddyAgent
import os

def main():
    print("=" * 60)
    print("📚 STUDY BUDDY AGENT - Command Line Demo")
    print("=" * 60)
    print("Your AI-powered study companion built with LangChain and Grok")
    print()
    
    # Check API key
    if not os.getenv("GROK_API_KEY"):
        print("❌ Error: GROK_API_KEY not found in environment variables!")
        print("Please create a .env file with your Grok API key.")
        return
    
    # Initialize agent
    print("🔄 Initializing Study Buddy Agent...")
    try:
        agent = StudyBuddyAgent()
        print("✅ Study Buddy Agent initialized successfully!")
    except Exception as e:
        print(f"❌ Failed to initialize agent: {str(e)}")
        return
    
    print()
    print("💡 What I can help you with:")
    print("   • Create personalized study plans")
    print("   • Generate quiz questions on any topic")
    print("   • Provide study tips and learning strategies")
    print("   • Track your study progress")
    print()
    print("📝 Example questions:")
    print("   • 'Create a study plan for my biology exam in 2 weeks'")
    print("   • 'Generate quiz questions about World War 2'")
    print("   • 'Give me study tips for memorizing formulas'")
    print("   • 'What should I study next?'")
    print()
    print("Type 'quit' or 'exit' to end the conversation.")
    print("=" * 60)
    
    # Chat loop
    while True:
        try:
            # Get user input
            user_input = input("\n🎓 You: ").strip()
            
            # Check for exit commands
            if user_input.lower() in ['quit', 'exit', 'bye', 'goodbye']:
                print("\n👋 Thanks for studying with me! Good luck with your learning!")
                break
            
            if not user_input:
                continue
            
            # Get agent response
            print("\n🤖 Study Buddy: ", end="")
            response = agent.chat(user_input)
            print(response)
            
        except KeyboardInterrupt:
            print("\n\n👋 Thanks for studying with me! Good luck with your learning!")
            break
        except Exception as e:
            print(f"\n❌ Error: {str(e)}")
            print("Please try again with a different question.")

if __name__ == "__main__":
    main()
