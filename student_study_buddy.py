# my study buddy agent for assignment
# using langchain framework with groq api

import os
import requests
from dotenv import load_dotenv
from langchain.llms.base import LLM
from langchain.agents import initialize_agent, Tool, AgentType
from langchain.memory import ConversationBufferWindowMemory

load_dotenv()

class GroqLLM(LLM):
    api_key: str = ""
    base_url: str = ""
    
    def __init__(self):
        super().__init__()
        self.api_key = os.getenv("GROQ_API_KEY")
        self.base_url = "https://api.groq.com/openai/v1"
    
    @property
    def _llm_type(self) -> str:
        return "groq"
    
    def _call(self, prompt: str, stop=None) -> str:
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": "llama3-8b-8192",
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": 500
        }
        
        try:
            response = requests.post(f"{self.base_url}/chat/completions", headers=headers, json=data)
            result = response.json()
            return result["choices"][0]["message"]["content"]
        except:
            return "sorry, something went wrong"

# study tools
def make_study_plan(subject):
    plans = {
        "calculus": """Detailed Calculus Study Plan (12 Weeks):

Week 1-2: Functions and Limits
• Day 1-3: Review function types (polynomial, rational, trig)
• Day 4-5: Understand limit concept and notation
• Day 6-7: Practice limit calculations and one-sided limits
• Weekend: Continuity and discontinuities

Week 3-4: Derivatives
• Day 1-2: Definition of derivative and geometric interpretation
• Day 3-4: Power rule, product rule, quotient rule
• Day 5-6: Chain rule and implicit differentiation
• Day 7: Derivatives of trig, exponential, and log functions

Week 5-6: Applications of Derivatives
• Day 1-2: Related rates problems
• Day 3-4: Optimization problems (max/min)
• Day 5-6: Curve sketching and critical points
• Day 7: L'Hôpital's rule

Week 7-8: Integration Basics
• Day 1-2: Antiderivatives and indefinite integrals
• Day 3-4: Fundamental Theorem of Calculus
• Day 5-6: Substitution method
• Day 7: Integration by parts

Week 9-10: Advanced Integration
• Day 1-2: Trigonometric integrals
• Day 3-4: Partial fractions
• Day 5-6: Improper integrals
• Day 7: Numerical integration methods

Week 11-12: Applications and Review
• Day 1-2: Area between curves
• Day 3-4: Volume of solids of revolution
• Day 5-6: Comprehensive review
• Day 7: Practice exams and final preparation

Daily Schedule:
• 1 hour theory review
• 1 hour problem solving
• 30 minutes reviewing previous day's work""",

        "math": """Comprehensive Math Study Plan (10 Weeks):

Week 1-2: Algebra Foundation
• Day 1-2: Order of operations and basic equations
• Day 3-4: Linear equations and inequalities
• Day 5-6: Systems of equations
• Day 7: Quadratic equations and factoring

Week 3-4: Functions and Graphs
• Day 1-2: Function notation and domain/range
• Day 3-4: Linear and quadratic functions
• Day 5-6: Exponential and logarithmic functions
• Day 7: Graphing techniques

Week 5-6: Trigonometry
• Day 1-2: Unit circle and basic trig functions
• Day 3-4: Trig identities and equations
• Day 5-6: Law of sines and cosines
• Day 7: Applications of trigonometry

Week 7-8: Pre-Calculus Topics
• Day 1-2: Polynomial and rational functions
• Day 3-4: Sequences and series
• Day 5-6: Limits and continuity introduction
• Day 7: Conic sections

Week 9-10: Problem Solving and Review
• Day 1-2: Word problems and applications
• Day 3-4: Mixed practice problems
• Day 5-6: Test-taking strategies
• Day 7: Final review and practice tests

Study Tips:
• Spend 2 hours daily on math
• Do 15-20 problems per topic
• Review previous topics weekly""",

        "algebra": """Detailed Algebra Study Plan (8 Weeks):

Week 1: Basic Operations
• Day 1-2: Integer operations and order of operations
• Day 3-4: Fractions and decimals
• Day 5-6: Exponents and scientific notation
• Day 7: Variables and algebraic expressions

Week 2: Linear Equations
• Day 1-2: Solving one-step and two-step equations
• Day 3-4: Multi-step equations with variables on both sides
• Day 5-6: Equations with fractions and decimals
• Day 7: Word problems with linear equations

Week 3: Inequalities and Absolute Value
• Day 1-2: Solving linear inequalities
• Day 3-4: Compound inequalities
• Day 5-6: Absolute value equations and inequalities
• Day 7: Graphing inequalities on number line

Week 4: Systems of Equations
• Day 1-2: Graphing method
• Day 3-4: Substitution method
• Day 5-6: Elimination method
• Day 7: Word problems with systems

Week 5: Polynomials
• Day 1-2: Adding and subtracting polynomials
• Day 3-4: Multiplying polynomials and FOIL
• Day 5-6: Factoring techniques
• Day 7: Factoring special cases

Week 6: Quadratic Equations
• Day 1-2: Solving by factoring
• Day 3-4: Completing the square
• Day 5-6: Quadratic formula
• Day 7: Applications of quadratics

Week 7: Functions and Graphing
• Day 1-2: Function notation and evaluation
• Day 3-4: Linear functions and slope
• Day 5-6: Graphing linear equations
• Day 7: Parallel and perpendicular lines

Week 8: Review and Applications
• Day 1-2: Mixed review problems
• Day 3-4: Real-world applications
• Day 5-6: Practice tests
• Day 7: Final preparation

Daily Routine:
• 45 minutes new material
• 45 minutes practice problems
• 15 minutes review previous concepts"""
    }
    return plans.get(subject.lower(), f"Study plan for {subject}:\n1. Review fundamentals\n2. Practice problems daily\n3. Take quizzes\n4. Review mistakes\n5. Get help when stuck")

def make_quiz(topic):
    quizzes = {
        "math": "Math Quiz:\nQ1: Solve for x: 2x + 5 = 13\nQ2: What is the derivative of x²?\nQ3: Find the area under y=x from 0 to 2",
        "calculus": "Calculus Quiz:\nQ1: What is lim(x→0) sin(x)/x?\nQ2: Find d/dx of ln(x)\nQ3: Integrate ∫x² dx",
        "algebra": "Algebra Quiz:\nQ1: Factor x² - 9\nQ2: Solve 3x - 7 = 14\nQ3: Graph y = 2x + 1"
    }
    return quizzes.get(topic.lower(), f"Quiz for {topic}:\nQ1: What is the main concept?\nQ2: Give an example\nQ3: How do you solve problems?")

def get_study_tips(subject):
    tips = {
        "math": "Math Study Tips:\n• Practice problems every day\n• Use Khan Academy videos\n• Make formula sheets\n• Join study groups\n• Ask teacher for help\n• Review mistakes carefully",
        "calculus": "Calculus Tips:\n• Understand concepts before memorizing\n• Draw graphs to visualize\n• Practice derivatives daily\n• Use online calculators to check work\n• Study with classmates",
        "algebra": "Algebra Tips:\n• Master basic operations first\n• Check your work by substituting\n• Use graphing calculator\n• Practice word problems\n• Make flashcards for formulas"
    }
    return tips.get(subject.lower(), f"Study tips for {subject}:\n• Practice regularly\n• Use multiple resources\n• Form study groups\n• Ask questions\n• Review often")

# setup tools
tools = [
    Tool(name="study_plan", description="creates study plan", func=make_study_plan),
    Tool(name="quiz", description="makes quiz questions", func=make_quiz),
    Tool(name="tips", description="gives study tips", func=get_study_tips)
]

class StudyBuddy:
    def __init__(self):
        self.llm = GroqLLM()
        self.memory = ConversationBufferWindowMemory(k=3)
        self.agent = initialize_agent(
            tools,
            self.llm,
            agent=AgentType.ZERO_SHOT_REACT_DESCRIPTION,
            memory=self.memory,
            verbose=False,
            handle_parsing_errors=True,
            max_iterations=2,
            early_stopping_method="generate"
        )
    
    def chat(self, message):
        # simple direct approach - check what user wants first
        message_lower = message.lower()

        # check for study plan requests
        if ("study" in message_lower and "plan" in message_lower) or ("plan" in message_lower and not "quiz" in message_lower):
            if "calculus" in message_lower:
                return make_study_plan("calculus")
            elif "math" in message_lower:
                return make_study_plan("math")
            elif "algebra" in message_lower:
                return make_study_plan("algebra")
            else:
                return make_study_plan("general")

        # check for quiz requests
        elif "quiz" in message_lower:
            if "calculus" in message_lower:
                return make_quiz("calculus")
            elif "math" in message_lower:
                return make_quiz("math")
            elif "algebra" in message_lower:
                return make_quiz("algebra")
            else:
                return make_quiz("general")

        # check for tips requests
        elif "tip" in message_lower:
            if "calculus" in message_lower:
                return get_study_tips("calculus")
            elif "math" in message_lower:
                return get_study_tips("math")
            elif "algebra" in message_lower:
                return get_study_tips("algebra")
            else:
                return get_study_tips("general")

        # try agent as fallback
        else:
            try:
                response = self.agent.run(message)
                return response
            except:
                return "I can help with study plans, quizzes, and study tips. What would you like?"

def main():
    print("Study Buddy Agent")
    print("type quit to exit")
    
    try:
        buddy = StudyBuddy()
        print("ready!")
        
        while True:
            user_input = input("\nYou: ")
            if user_input.lower() == 'quit':
                break
            
            response = buddy.chat(user_input)
            print(f"\nBot: {response}")
            print("-" * 40)
            
    except Exception as e:
        print(f"error starting: {e}")

if __name__ == "__main__":
    main()
