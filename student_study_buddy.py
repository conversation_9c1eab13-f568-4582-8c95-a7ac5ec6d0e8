# my study buddy agent for assignment
# using langchain framework with groq api

import os
import requests
from dotenv import load_dotenv
from langchain.llms.base import LLM
from langchain.agents import initialize_agent, Tool, AgentType
from langchain.memory import ConversationBufferWindowMemory

load_dotenv()

class GroqLLM(LLM):
    api_key: str = ""
    base_url: str = ""
    
    def __init__(self):
        super().__init__()
        self.api_key = os.getenv("GROQ_API_KEY")
        self.base_url = "https://api.groq.com/openai/v1"
    
    @property
    def _llm_type(self) -> str:
        return "groq"
    
    def _call(self, prompt: str, stop=None) -> str:
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": "llama3-8b-8192",
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": 500
        }
        
        try:
            response = requests.post(f"{self.base_url}/chat/completions", headers=headers, json=data)
            result = response.json()
            return result["choices"][0]["message"]["content"]
        except:
            return "sorry, something went wrong"

# study tools
def make_study_plan(subject):
    plans = {
        "math": "Math Study Plan:\n1. Review algebra basics (equations, graphing)\n2. Practice calculus (derivatives, integrals)\n3. Do 20 problems daily\n4. Take practice tests weekly\n5. Review mistakes and weak areas",
        "calculus": "Calculus Study Plan:\n1. Master limits and continuity\n2. Learn derivatives and rules\n3. Practice integration techniques\n4. Study applications (optimization, related rates)\n5. Do past exam problems",
        "algebra": "Algebra Study Plan:\n1. Review basic operations\n2. Master solving equations\n3. Learn graphing functions\n4. Practice word problems\n5. Study systems of equations"
    }
    return plans.get(subject.lower(), f"Study plan for {subject}:\n1. Review fundamentals\n2. Practice problems daily\n3. Take quizzes\n4. Review mistakes\n5. Get help when stuck")

def make_quiz(topic):
    quizzes = {
        "math": "Math Quiz:\nQ1: Solve for x: 2x + 5 = 13\nQ2: What is the derivative of x²?\nQ3: Find the area under y=x from 0 to 2",
        "calculus": "Calculus Quiz:\nQ1: What is lim(x→0) sin(x)/x?\nQ2: Find d/dx of ln(x)\nQ3: Integrate ∫x² dx",
        "algebra": "Algebra Quiz:\nQ1: Factor x² - 9\nQ2: Solve 3x - 7 = 14\nQ3: Graph y = 2x + 1"
    }
    return quizzes.get(topic.lower(), f"Quiz for {topic}:\nQ1: What is the main concept?\nQ2: Give an example\nQ3: How do you solve problems?")

def get_study_tips(subject):
    tips = {
        "math": "Math Study Tips:\n• Practice problems every day\n• Use Khan Academy videos\n• Make formula sheets\n• Join study groups\n• Ask teacher for help\n• Review mistakes carefully",
        "calculus": "Calculus Tips:\n• Understand concepts before memorizing\n• Draw graphs to visualize\n• Practice derivatives daily\n• Use online calculators to check work\n• Study with classmates",
        "algebra": "Algebra Tips:\n• Master basic operations first\n• Check your work by substituting\n• Use graphing calculator\n• Practice word problems\n• Make flashcards for formulas"
    }
    return tips.get(subject.lower(), f"Study tips for {subject}:\n• Practice regularly\n• Use multiple resources\n• Form study groups\n• Ask questions\n• Review often")

# setup tools
tools = [
    Tool(name="study_plan", description="creates study plan", func=make_study_plan),
    Tool(name="quiz", description="makes quiz questions", func=make_quiz),
    Tool(name="tips", description="gives study tips", func=get_study_tips)
]

class StudyBuddy:
    def __init__(self):
        self.llm = GroqLLM()
        self.memory = ConversationBufferWindowMemory(k=3)
        self.agent = initialize_agent(
            tools,
            self.llm,
            agent=AgentType.ZERO_SHOT_REACT_DESCRIPTION,
            memory=self.memory,
            verbose=False,
            handle_parsing_errors=True,
            max_iterations=2,
            early_stopping_method="generate"
        )
    
    def chat(self, message):
        # simple direct approach - check what user wants first
        message_lower = message.lower()

        # check for study plan requests
        if ("study" in message_lower and "plan" in message_lower) or ("plan" in message_lower and not "quiz" in message_lower):
            if "calculus" in message_lower:
                return make_study_plan("calculus")
            elif "math" in message_lower:
                return make_study_plan("math")
            elif "algebra" in message_lower:
                return make_study_plan("algebra")
            else:
                return make_study_plan("general")

        # check for quiz requests
        elif "quiz" in message_lower:
            if "calculus" in message_lower:
                return make_quiz("calculus")
            elif "math" in message_lower:
                return make_quiz("math")
            elif "algebra" in message_lower:
                return make_quiz("algebra")
            else:
                return make_quiz("general")

        # check for tips requests
        elif "tip" in message_lower:
            if "calculus" in message_lower:
                return get_study_tips("calculus")
            elif "math" in message_lower:
                return get_study_tips("math")
            elif "algebra" in message_lower:
                return get_study_tips("algebra")
            else:
                return get_study_tips("general")

        # try agent as fallback
        else:
            try:
                response = self.agent.run(message)
                return response
            except:
                return "I can help with study plans, quizzes, and study tips. What would you like?"

def main():
    print("Study Buddy Agent")
    print("type quit to exit")
    
    try:
        buddy = StudyBuddy()
        print("ready!")
        
        while True:
            user_input = input("\nYou: ")
            if user_input.lower() == 'quit':
                break
            
            response = buddy.chat(user_input)
            print(f"\nBot: {response}")
            print("-" * 40)
            
    except Exception as e:
        print(f"error starting: {e}")

if __name__ == "__main__":
    main()
