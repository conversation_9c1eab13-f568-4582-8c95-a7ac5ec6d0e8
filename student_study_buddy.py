# my study buddy agent for assignment
# using langchain framework with groq api

import os
import requests
from dotenv import load_dotenv
from langchain.llms.base import LLM
from langchain.agents import initialize_agent, Tool, AgentType
from langchain.memory import ConversationBufferWindowMemory

load_dotenv()

class GroqLLM(LLM):
    api_key: str = ""
    base_url: str = ""
    
    def __init__(self):
        super().__init__()
        self.api_key = os.getenv("GROQ_API_KEY")
        self.base_url = "https://api.groq.com/openai/v1"
    
    @property
    def _llm_type(self) -> str:
        return "groq"
    
    def _call(self, prompt: str, stop=None) -> str:
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": "llama3-8b-8192",
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": 500
        }
        
        try:
            response = requests.post(f"{self.base_url}/chat/completions", headers=headers, json=data)
            result = response.json()
            return result["choices"][0]["message"]["content"]
        except:
            return "sorry, something went wrong"

# study tools
def make_study_plan(subject):
    return f"Study plan for {subject}: 1) review basics 2) practice problems 3) take quiz 4) review mistakes"

def make_quiz(topic):
    return f"Quiz for {topic}: Q1: What is the main concept? Q2: Give an example Q3: How do you solve it?"

def get_study_tips(subject):
    return f"Tips for {subject}: use flashcards, practice daily, join study groups, ask questions"

# setup tools
tools = [
    Tool(name="study_plan", description="creates study plan", func=make_study_plan),
    Tool(name="quiz", description="makes quiz questions", func=make_quiz),
    Tool(name="tips", description="gives study tips", func=get_study_tips)
]

class StudyBuddy:
    def __init__(self):
        self.llm = GroqLLM()
        self.memory = ConversationBufferWindowMemory(k=3)
        self.agent = initialize_agent(
            tools,
            self.llm,
            agent=AgentType.ZERO_SHOT_REACT_DESCRIPTION,
            memory=self.memory,
            verbose=True,
            handle_parsing_errors=True
        )
    
    def chat(self, message):
        try:
            response = self.agent.run(message)
            return response
        except Exception as e:
            return f"error: {str(e)}"

def main():
    print("Study Buddy Agent")
    print("type quit to exit")
    
    try:
        buddy = StudyBuddy()
        print("ready!")
        
        while True:
            user_input = input("\nYou: ")
            if user_input.lower() == 'quit':
                break
            
            response = buddy.chat(user_input)
            print(f"\nBot: {response}")
            print("-" * 40)
            
    except Exception as e:
        print(f"error starting: {e}")

if __name__ == "__main__":
    main()
