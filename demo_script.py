"""
<PERSON><PERSON> for Study Buddy Agent Presentation
This script demonstrates all the key features of the LangChain Study Buddy Agent
"""

from study_buddy_agent import StudyBuddyAgent
import time
import os

def print_separator(title=""):
    """Print a nice separator for demo sections"""
    print("\n" + "=" * 60)
    if title:
        print(f"  {title}")
        print("=" * 60)
    else:
        print("=" * 60)

def demo_agent_initialization():
    """Demo 1: Agent Initialization"""
    print_separator("DEMO 1: AGENT INITIALIZATION")
    print("🔄 Initializing Study Buddy Agent with Grok API...")
    
    try:
        agent = StudyBuddyAgent()
        print("✅ Study Buddy Agent initialized successfully!")
        print("🤖 Agent is ready to help with studying!")
        return agent
    except Exception as e:
        print(f"❌ Error initializing agent: {e}")
        return None

def demo_study_plan_creation(agent):
    """Demo 2: Study Plan Creation"""
    print_separator("DEMO 2: STUDY PLAN CREATION")
    print("📝 User Request: 'Create a study plan for my calculus exam in 2 weeks'")
    print("🤖 Agent Response:")
    
    response = agent.chat("Create a study plan for my calculus exam in 2 weeks")
    print(response)
    
    time.sleep(2)

def demo_quiz_generation(agent):
    """Demo 3: Quiz Generation"""
    print_separator("DEMO 3: QUIZ GENERATION")
    print("📝 User Request: 'Generate 3 quiz questions about derivatives in calculus'")
    print("🤖 Agent Response:")
    
    response = agent.chat("Generate 3 quiz questions about derivatives in calculus")
    print(response)
    
    time.sleep(2)

def demo_study_tips(agent):
    """Demo 4: Study Tips"""
    print_separator("DEMO 4: STUDY TIPS")
    print("📝 User Request: 'Give me study tips for memorizing mathematical formulas'")
    print("🤖 Agent Response:")
    
    response = agent.chat("Give me study tips for memorizing mathematical formulas")
    print(response)
    
    time.sleep(2)

def demo_session_logging(agent):
    """Demo 5: Study Session Logging"""
    print_separator("DEMO 5: STUDY SESSION LOGGING")
    print("📝 User Request: 'I just studied calculus for 45 minutes'")
    print("🤖 Agent Response:")
    
    response = agent.chat("I just studied calculus for 45 minutes")
    print(response)
    
    # Show that the session was logged
    print("\n📊 Progress Update:")
    summary = agent.progress_tracker.get_progress_summary()
    print(f"   Total Study Hours: {summary['total_study_hours']}")
    print(f"   Current Streak: {summary['current_streak']} days")
    
    time.sleep(2)

def demo_progress_tracking(agent):
    """Demo 6: Progress Tracking"""
    print_separator("DEMO 6: PROGRESS TRACKING")
    print("📝 User Request: 'What should I study next based on my progress?'")
    print("🤖 Agent Response:")
    
    response = agent.chat("What should I study next based on my progress?")
    print(response)
    
    # Show detailed progress
    print("\n📈 Detailed Progress Summary:")
    summary = agent.progress_tracker.get_progress_summary()
    for key, value in summary.items():
        print(f"   {key.replace('_', ' ').title()}: {value}")
    
    time.sleep(2)

def demo_memory_and_context(agent):
    """Demo 7: Memory and Context"""
    print_separator("DEMO 7: MEMORY AND CONTEXT")
    print("📝 User Request: 'What subjects have I been studying?'")
    print("🤖 Agent Response:")
    
    response = agent.chat("What subjects have I been studying?")
    print(response)
    
    print("\n📝 Follow-up Request: 'Create more quiz questions for my weakest subject'")
    print("🤖 Agent Response:")
    
    response = agent.chat("Create more quiz questions for my weakest subject")
    print(response)
    
    time.sleep(2)

def demo_recommendations(agent):
    """Demo 8: Personalized Recommendations"""
    print_separator("DEMO 8: PERSONALIZED RECOMMENDATIONS")
    
    recommendations = agent.progress_tracker.get_recommendations()
    print("🎯 Personalized Study Recommendations:")
    for i, rec in enumerate(recommendations, 1):
        print(f"   {i}. {rec}")
    
    if not recommendations:
        print("   No specific recommendations yet - start studying to get personalized advice!")

def demo_langchain_features(agent):
    """Demo 9: LangChain Framework Features"""
    print_separator("DEMO 9: LANGCHAIN FRAMEWORK FEATURES")
    
    print("🔧 LangChain Features Demonstrated:")
    print("   ✅ Custom LLM Integration (Grok API)")
    print("   ✅ Agent with Multiple Tools")
    print("   ✅ Conversation Memory")
    print("   ✅ Tool Selection and Execution")
    print("   ✅ Error Handling")
    
    print("\n🛠️ Available Tools:")
    for tool in agent.tools:
        print(f"   • {tool.name}: {tool.description}")
    
    print(f"\n🧠 Memory: Remembers last {agent.memory.k} conversation exchanges")
    print(f"💾 Progress Data: Stored in {agent.progress_tracker.data_file}")

def main():
    """Main demo function"""
    print_separator("LANGCHAIN STUDY BUDDY AGENT DEMO")
    print("🎓 Demonstrating AI Agent Framework Capabilities")
    print("🔗 Built with LangChain + Grok API")
    print("👨‍🎓 For Assignment 1 - AI Agent Frameworks")
    
    # Check environment
    if not os.getenv("GROK_API_KEY"):
        print("\n❌ Error: GROK_API_KEY not found!")
        print("Please create a .env file with your Grok API key.")
        return
    
    # Initialize agent
    agent = demo_agent_initialization()
    if not agent:
        return
    
    # Run all demos
    demo_study_plan_creation(agent)
    demo_quiz_generation(agent)
    demo_study_tips(agent)
    demo_session_logging(agent)
    demo_progress_tracking(agent)
    demo_memory_and_context(agent)
    demo_recommendations(agent)
    demo_langchain_features(agent)
    
    # Conclusion
    print_separator("DEMO CONCLUSION")
    print("🎉 Study Buddy Agent Demo Complete!")
    print("\n📋 Key Features Demonstrated:")
    print("   ✅ Study plan creation")
    print("   ✅ Quiz question generation")
    print("   ✅ Study tips and strategies")
    print("   ✅ Study session logging")
    print("   ✅ Progress tracking and analytics")
    print("   ✅ Conversation memory")
    print("   ✅ Personalized recommendations")
    print("   ✅ LangChain framework integration")
    
    print("\n🚀 Next Steps for Live Demo:")
    print("   1. Run: streamlit run app.py")
    print("   2. Show web interface")
    print("   3. Demonstrate real-time interaction")
    print("   4. Show progress tracking in sidebar")
    
    print("\n💡 This demonstrates LangChain's power for building")
    print("   intelligent, stateful AI agents with custom tools!")

if __name__ == "__main__":
    main()
