"""
Simple Study Buddy Agent using LangChain Framework with Groq API
A basic AI study companion that helps students with studying.
"""

import os
import requests
from dotenv import load_dotenv
from langchain.llms.base import LLM
from langchain.agents import initialize_agent, Tool, AgentType
from langchain.memory import ConversationBufferWindowMemory

# Load environment variables
load_dotenv()

class GroqLLM(LLM):
    """Simple LLM wrapper for Groq API"""

    api_key: str = ""
    base_url: str = ""

    def __init__(self):
        super().__init__()
        self.api_key = os.getenv("GROQ_API_KEY")
        self.base_url = "https://api.groq.com/openai/v1"

        if not self.api_key:
            raise ValueError("GROQ_API_KEY not found in environment variables")
    
    @property
    def _llm_type(self) -> str:
        return "groq"
    
    def _call(self, prompt: str, stop=None) -> str:
        """Call Groq API with the given prompt"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": "llama3-8b-8192",
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": 1000,
            "temperature": 0.7
        }
        
        try:
            response = requests.post(f"{self.base_url}/chat/completions", 
                                   headers=headers, json=data, timeout=30)
            response.raise_for_status()
            result = response.json()
            return result["choices"][0]["message"]["content"]
        
        except Exception as e:
            return f"Error: {str(e)}"

class SimpleStudyBuddy:
    """Simple Study Buddy using LangChain"""
    
    def __init__(self):
        # Initialize LLM
        self.llm = GroqLLM()
        
        # Create simple tools
        self.tools = [
            Tool(
                name="create_study_plan",
                description="Create a study plan for a subject",
                func=self.create_study_plan
            ),
            Tool(
                name="generate_quiz",
                description="Generate quiz questions on a topic",
                func=self.generate_quiz
            ),
            Tool(
                name="study_tips",
                description="Provide study tips and techniques",
                func=self.study_tips
            )
        ]
        
        # Initialize memory
        self.memory = ConversationBufferWindowMemory(
            memory_key="chat_history",
            k=5,  # Remember last 5 exchanges
            return_messages=True
        )
        
        # Initialize agent
        self.agent = initialize_agent(
            tools=self.tools,
            llm=self.llm,
            agent=AgentType.CONVERSATIONAL_REACT_DESCRIPTION,
            memory=self.memory,
            verbose=True
        )
    
    def create_study_plan(self, query: str) -> str:
        """Create a study plan"""
        prompt = f"""Create a detailed study plan for: {query}
        
        Include:
        - Timeline and schedule
        - Key topics to cover
        - Study methods
        - Goals and milestones
        
        Make it practical and achievable."""
        
        return self.llm._call(prompt)
    
    def generate_quiz(self, query: str) -> str:
        """Generate quiz questions"""
        prompt = f"""Generate 5 quiz questions about: {query}
        
        Include:
        - Multiple choice questions
        - Short answer questions
        - One essay question
        
        Provide answers at the end."""
        
        return self.llm._call(prompt)
    
    def study_tips(self, query: str) -> str:
        """Provide study tips"""
        prompt = f"""Provide effective study tips for: {query}
        
        Include:
        - Memory techniques
        - Time management
        - Active learning strategies
        - Motivation tips
        
        Make them practical and actionable."""
        
        return self.llm._call(prompt)
    
    def chat(self, message: str) -> str:
        """Chat with the study buddy"""
        try:
            response = self.agent.run(message)
            return response
        except Exception as e:
            return f"Sorry, I encountered an error: {str(e)}"

def main():
    """Simple CLI demo"""
    print("🎓 Simple Study Buddy Agent")
    print("Built with LangChain + Groq API")
    print("Type 'quit' to exit\n")
    
    # Check API key
    if not os.getenv("GROQ_API_KEY"):
        print("❌ Error: GROQ_API_KEY not found!")
        print("Please create a .env file with your Groq API key.")
        return
    
    try:
        # Initialize agent
        study_buddy = SimpleStudyBuddy()
        print("✅ Study Buddy initialized successfully!\n")
        
        # Chat loop
        while True:
            user_input = input("You: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'bye']:
                print("👋 Happy studying!")
                break
            
            if not user_input:
                continue
            
            print("\n🤖 Study Buddy:")
            response = study_buddy.chat(user_input)
            print(response)
            print("\n" + "-" * 50 + "\n")
    
    except Exception as e:
        print(f"❌ Error initializing Study Buddy: {e}")

if __name__ == "__main__":
    main()
