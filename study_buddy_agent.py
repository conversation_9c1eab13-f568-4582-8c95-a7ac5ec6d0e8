"""
<PERSON><PERSON><PERSON><PERSON> Study Buddy Agent using Grok API
A personalized AI study assistant that helps with study plans, quiz generation, and learning tips.
"""

import os
from typing import Dict, List, Any
from dotenv import load_dotenv
from langchain.agents import AgentType, initialize_agent
from langchain.memory import ConversationBufferWindowMemory
from langchain.tools import Tool
from langchain.schema import BaseMessage
from langchain_community.llms.base import LLM
import requests
import json
from progress_tracker import StudyProgressTracker

# Load environment variables
load_dotenv()

class GrokLLM(LLM):
    """Custom LLM wrapper for Grok API"""
    
    def __init__(self):
        super().__init__()
        self.api_key = os.getenv("GROK_API_KEY")
        self.base_url = os.getenv("GROK_BASE_URL", "https://api.x.ai/v1")
        
        if not self.api_key:
            raise ValueError("GROK_API_KEY not found in environment variables")
    
    @property
    def _llm_type(self) -> str:
        return "grok"
    
    def _call(self, prompt: str, stop: List[str] = None) -> str:
        """Call Grok API with the given prompt"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": "grok-beta",
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "max_tokens": 1000,
            "temperature": 0.7
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )
            response.raise_for_status()
            
            result = response.json()
            return result["choices"][0]["message"]["content"]
            
        except requests.exceptions.RequestException as e:
            return f"Error calling Grok API: {str(e)}"
        except (KeyError, IndexError) as e:
            return f"Error parsing Grok API response: {str(e)}"

class StudyBuddyAgent:
    """Main Study Buddy Agent class"""
    
    def __init__(self):
        # Initialize Grok LLM
        self.llm = GrokLLM()
        
        # Initialize memory to remember conversation
        self.memory = ConversationBufferWindowMemory(
            memory_key="chat_history",
            k=10,  # Remember last 10 exchanges
            return_messages=True
        )
        
        # Initialize tools
        self.tools = self._create_tools()
        
        # Initialize the agent
        self.agent = initialize_agent(
            tools=self.tools,
            llm=self.llm,
            agent=AgentType.CONVERSATIONAL_REACT_DESCRIPTION,
            memory=self.memory,
            verbose=True,
            handle_parsing_errors=True
        )
        
        # Initialize progress tracker
        self.progress_tracker = StudyProgressTracker()

        # Store user study data (legacy - keeping for compatibility)
        self.user_data = {
            "subjects": [],
            "study_goals": [],
            "completed_topics": [],
            "preferences": {}
        }
    
    def _create_tools(self) -> List[Tool]:
        """Create the tools that the agent can use"""
        
        def create_study_plan(input_text: str) -> str:
            """Create a personalized study plan based on subject and timeframe"""
            prompt = f"""
            Create a detailed study plan based on this request: {input_text}
            
            Please provide:
            1. A structured timeline with daily/weekly goals
            2. Specific topics to cover in order
            3. Recommended study methods for each topic
            4. Time allocation suggestions
            5. Milestones and checkpoints
            
            Format the response clearly with headers and bullet points.
            """
            return self.llm._call(prompt)
        
        def generate_quiz(input_text: str) -> str:
            """Generate quiz questions on a specific topic"""
            prompt = f"""
            Generate quiz questions based on: {input_text}
            
            Please provide:
            1. 5 multiple choice questions with 4 options each
            2. 3 short answer questions
            3. 2 essay/discussion questions
            4. Include the correct answers at the end
            
            Make questions at different difficulty levels (easy, medium, hard).
            """
            return self.llm._call(prompt)
        
        def provide_study_tips(input_text: str) -> str:
            """Provide study tips and learning strategies"""
            prompt = f"""
            Provide study tips and learning strategies for: {input_text}
            
            Include:
            1. Memory techniques and mnemonics
            2. Active learning strategies
            3. Time management tips
            4. Note-taking methods
            5. Test preparation strategies
            6. Ways to stay motivated
            
            Make the advice practical and actionable.
            """
            return self.llm._call(prompt)
        
        def track_progress(input_text: str) -> str:
            """Track study progress and suggest next steps"""
            # Get comprehensive progress data
            summary = self.progress_tracker.get_progress_summary()
            recommendations = self.progress_tracker.get_recommendations()

            # Get subject details
            subject_details = []
            for subject_name in self.progress_tracker.data["subjects"].keys():
                subject_progress = self.progress_tracker.get_subject_progress(subject_name)
                if subject_progress:
                    subject_details.append(f"- {subject_name}: {subject_progress['completed_topics']}/{subject_progress['total_topics']} topics completed, {subject_progress['hours_studied']:.1f} hours studied")

            subjects_info = "\n".join(subject_details) if subject_details else "No subjects tracked yet"
            recommendations_text = "\n".join([f"• {rec}" for rec in recommendations])

            prompt = f"""
            Based on the user's detailed study progress:

            OVERALL PROGRESS:
            - Total study hours: {summary['total_study_hours']}
            - Subjects: {summary['total_subjects']}
            - Topics completed: {summary['completed_topics']}/{summary['total_topics']} ({summary['completion_rate']}%)
            - Current streak: {summary['current_streak']} days
            - This week: {summary['sessions_this_week']} sessions, {summary['hours_this_week']} hours

            SUBJECT BREAKDOWN:
            {subjects_info}

            PERSONALIZED RECOMMENDATIONS:
            {recommendations_text}

            Current request: {input_text}

            Please provide:
            1. A celebration of their achievements and progress
            2. Specific next steps based on their data
            3. Personalized study suggestions
            4. Motivation and encouragement
            5. Any areas that need attention

            Make it personal and encouraging!
            """
            return self.llm._call(prompt)

        def log_study_session(input_text: str) -> str:
            """Log a completed study session"""
            prompt = f"""
            The user wants to log a study session: {input_text}

            Please extract the following information and confirm:
            1. Subject studied
            2. Topic or chapter covered
            3. Duration (in minutes)
            4. Any notes or observations

            If any information is missing, ask the user to provide it.
            If all information is provided, confirm that the session has been logged.

            Format your response to be encouraging and acknowledge their effort!
            """

            # Try to extract session info (simplified extraction)
            # In a real app, you'd use more sophisticated NLP
            words = input_text.lower().split()

            # Look for duration indicators
            duration = 60  # default
            for i, word in enumerate(words):
                if word in ["minutes", "mins", "min"] and i > 0:
                    try:
                        duration = int(words[i-1])
                        break
                    except ValueError:
                        pass
                elif word in ["hour", "hours"] and i > 0:
                    try:
                        duration = int(words[i-1]) * 60
                        break
                    except ValueError:
                        pass

            # For demo purposes, log a sample session
            # In a real app, you'd extract actual subject/topic from the text
            if "math" in input_text.lower() or "calculus" in input_text.lower():
                self.progress_tracker.log_study_session("Mathematics", "Calculus", duration, "Study session logged via agent")
            elif "science" in input_text.lower() or "biology" in input_text.lower():
                self.progress_tracker.log_study_session("Biology", "General Study", duration, "Study session logged via agent")
            else:
                self.progress_tracker.log_study_session("General", "Study Session", duration, "Study session logged via agent")

            return self.llm._call(prompt)

        return [
            Tool(
                name="create_study_plan",
                description="Creates personalized study plans and schedules. Use when user asks for study plans, schedules, or how to organize their studying.",
                func=create_study_plan
            ),
            Tool(
                name="generate_quiz",
                description="Generates practice questions and quizzes on any topic. Use when user wants practice questions, tests, or wants to check their knowledge.",
                func=generate_quiz
            ),
            Tool(
                name="provide_study_tips",
                description="Provides study techniques, memory tips, and learning strategies. Use when user asks for study methods, tips, or how to learn better.",
                func=provide_study_tips
            ),
            Tool(
                name="track_progress",
                description="Tracks study progress and suggests next steps. Use when user wants to know what to study next or review their progress.",
                func=track_progress
            ),
            Tool(
                name="log_study_session",
                description="Logs a completed study session with subject, topic, and duration. Use when user mentions they studied something or completed a study session.",
                func=log_study_session
            )
        ]
    
    def chat(self, message: str) -> str:
        """Main chat interface for the Study Buddy Agent"""
        try:
            # Update user data based on the message
            self._update_user_data(message)
            
            # Get response from the agent
            response = self.agent.run(input=message)
            
            return response
            
        except Exception as e:
            return f"I'm sorry, I encountered an error: {str(e)}. Please try rephrasing your question."
    
    def _update_user_data(self, message: str):
        """Update user data and progress tracker based on their messages"""
        message_lower = message.lower()

        # Extract subjects mentioned
        common_subjects = {
            "math": "Mathematics", "mathematics": "Mathematics", "calculus": "Mathematics",
            "algebra": "Mathematics", "geometry": "Mathematics",
            "science": "Science", "physics": "Physics", "chemistry": "Chemistry", "biology": "Biology",
            "history": "History", "english": "English", "literature": "Literature", "writing": "English",
            "computer science": "Computer Science", "programming": "Computer Science", "coding": "Computer Science",
            "psychology": "Psychology", "sociology": "Sociology", "economics": "Economics",
            "spanish": "Spanish", "french": "French", "german": "German", "language": "Languages"
        }

        for keyword, subject_name in common_subjects.items():
            if keyword in message_lower:
                # Add to legacy data
                if subject_name not in self.user_data["subjects"]:
                    self.user_data["subjects"].append(subject_name)

                # Add to progress tracker
                self.progress_tracker.add_subject(subject_name)

        # Extract completed topics and update progress tracker
        if "completed" in message_lower or "finished" in message_lower:
            words = message_lower.split()
            for i, word in enumerate(words):
                if word in ["completed", "finished"] and i < len(words) - 1:
                    topic = words[i + 1]
                    if topic not in self.user_data["completed_topics"]:
                        self.user_data["completed_topics"].append(topic)

                    # Update in progress tracker for the most recently mentioned subject
                    if self.user_data["subjects"]:
                        recent_subject = self.user_data["subjects"][-1]
                        self.progress_tracker.add_topic(recent_subject, topic, "completed")

        # Extract study goals
        if any(goal_word in message_lower for goal_word in ["goal", "exam", "test", "deadline"]):
            # Extract potential dates and goals
            if "exam" in message_lower or "test" in message_lower:
                # Look for time indicators
                time_indicators = ["week", "weeks", "month", "months", "day", "days"]
                for indicator in time_indicators:
                    if indicator in message_lower:
                        goal_desc = f"Prepare for exam mentioned in conversation"
                        if self.user_data["subjects"]:
                            subject = self.user_data["subjects"][-1]
                            goal_desc = f"Prepare for {subject} exam"

                        # Add goal with estimated target date
                        from datetime import datetime, timedelta
                        target_date = (datetime.now() + timedelta(weeks=2)).isoformat()
                        self.progress_tracker.add_goal(goal_desc, target_date,
                                                     self.user_data["subjects"][-1] if self.user_data["subjects"] else None)
                        break

if __name__ == "__main__":
    # Simple test
    agent = StudyBuddyAgent()
    print("Study Buddy Agent initialized successfully!")
    print("You can now use this agent in your Streamlit app.")
