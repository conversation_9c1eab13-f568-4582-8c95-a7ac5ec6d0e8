"""
Streamlit interface for the Study Buddy Agent
"""

import streamlit as st
import os
from study_buddy_agent import StudyBuddyAgent

# Page configuration
st.set_page_config(
    page_title="Study Buddy Agent",
    page_icon="📚",
    layout="wide"
)

# Title and description
st.title("📚 Study Buddy Agent")
st.markdown("*Your AI-powered study companion built with LangChain and Grok*")

# Sidebar for information and progress
with st.sidebar:
    st.header("🎯 What I Can Help With")
    st.markdown("""
    - **Study Plans**: Create personalized study schedules
    - **Quiz Generation**: Practice questions on any topic
    - **Study Tips**: Learning strategies and memory techniques
    - **Progress Tracking**: Keep track of your learning journey
    - **Session Logging**: Record your study sessions
    """)

    # Progress will be shown after agent initialization

    st.header("💡 Example Questions")
    st.markdown("""
    - "Create a study plan for my calculus exam in 2 weeks"
    - "Generate quiz questions about photosynthesis"
    - "Give me study tips for memorizing historical dates"
    - "I just studied math for 45 minutes"
    - "What should I study next?"
    """)

    st.header("⚙️ Setup")
    st.markdown("""
    Make sure you have:
    1. Created a `.env` file with your Grok API key
    2. Installed all requirements: `pip install -r requirements.txt`
    """)

# Check if API key is configured
if not os.getenv("GROQ_API_KEY"):
    st.error("⚠️ Groq API key not found! Please create a `.env` file with your GROQ_API_KEY.")
    st.stop()

# Initialize the agent
@st.cache_resource
def initialize_agent():
    """Initialize the Study Buddy Agent (cached for performance)"""
    try:
        return StudyBuddyAgent()
    except Exception as e:
        st.error(f"Failed to initialize Study Buddy Agent: {str(e)}")
        return None

agent = initialize_agent()

if agent is None:
    st.stop()

# Update sidebar with progress after agent is initialized
with st.sidebar:
    try:
        progress_summary = agent.progress_tracker.get_progress_summary()

        st.header("📊 Your Progress")
        col1, col2 = st.columns(2)
        with col1:
            st.metric("Study Hours", f"{progress_summary['total_study_hours']}")
            st.metric("Current Streak", f"{progress_summary['current_streak']} days")
        with col2:
            st.metric("Subjects", progress_summary['total_subjects'])
            st.metric("Completion Rate", f"{progress_summary['completion_rate']}%")

        if progress_summary['total_topics'] > 0:
            st.progress(progress_summary['completion_rate'] / 100)
            st.caption(f"{progress_summary['completed_topics']}/{progress_summary['total_topics']} topics completed")

        # Show recommendations
        recommendations = agent.progress_tracker.get_recommendations()
        if recommendations:
            st.header("💡 Recommendations")
            for rec in recommendations:
                st.info(rec)

    except Exception as e:
        st.sidebar.caption("Progress tracking will appear after you start studying!")

# Initialize chat history
if "messages" not in st.session_state:
    st.session_state.messages = [
        {"role": "assistant", "content": "Hi! I'm your Study Buddy Agent. I can help you create study plans, generate quiz questions, provide study tips, and track your progress. What would you like to study today?"}
    ]

# Display chat messages
for message in st.session_state.messages:
    with st.chat_message(message["role"]):
        st.markdown(message["content"])

# Chat input
if prompt := st.chat_input("Ask me anything about studying..."):
    # Add user message to chat history
    st.session_state.messages.append({"role": "user", "content": prompt})
    
    # Display user message
    with st.chat_message("user"):
        st.markdown(prompt)
    
    # Get agent response
    with st.chat_message("assistant"):
        with st.spinner("Thinking..."):
            try:
                response = agent.chat(prompt)
                st.markdown(response)
                
                # Add assistant response to chat history
                st.session_state.messages.append({"role": "assistant", "content": response})
                
            except Exception as e:
                error_msg = f"I'm sorry, I encountered an error: {str(e)}. Please try again."
                st.error(error_msg)
                st.session_state.messages.append({"role": "assistant", "content": error_msg})

# Footer
st.markdown("---")
st.markdown("*Built with LangChain, Streamlit, and Grok API*")

# Quick action buttons
st.markdown("### 🚀 Quick Actions")
col1, col2, col3, col4, col5 = st.columns(5)

with col1:
    if st.button("📅 Create Study Plan"):
        prompt = "I need help creating a study plan. Can you ask me about my subject, timeline, and goals?"
        st.session_state.messages.append({"role": "user", "content": prompt})
        st.rerun()

with col2:
    if st.button("❓ Generate Quiz"):
        prompt = "I'd like some practice questions. Can you help me generate a quiz?"
        st.session_state.messages.append({"role": "user", "content": prompt})
        st.rerun()

with col3:
    if st.button("💡 Study Tips"):
        prompt = "Can you give me some effective study tips and techniques?"
        st.session_state.messages.append({"role": "user", "content": prompt})
        st.rerun()

with col4:
    if st.button("📊 Check Progress"):
        prompt = "Can you help me review my study progress and suggest what to do next?"
        st.session_state.messages.append({"role": "user", "content": prompt})
        st.rerun()

with col5:
    if st.button("⏱️ Log Session"):
        prompt = "I want to log a study session I just completed."
        st.session_state.messages.append({"role": "user", "content": prompt})
        st.rerun()

# Clear chat button
if st.button("🗑️ Clear Chat History"):
    st.session_state.messages = [
        {"role": "assistant", "content": "Hi! I'm your Study Buddy Agent. I can help you create study plans, generate quiz questions, provide study tips, and track your progress. What would you like to study today?"}
    ]
    st.rerun()
